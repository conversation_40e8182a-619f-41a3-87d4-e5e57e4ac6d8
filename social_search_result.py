
import asyncio
import aiofiles
import base64
import hashlib
import json
import logging
import secrets
import io
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import httpx
import magic
import structlog
from cryptography.fernet import Fernet
from pydantic import BaseModel, Field, SecretStr, field_validator
from tenacity import AsyncRetrying, stop_after_attempt, wait_exponential, retry_if_exception_type

try:
    import face_recognition
    from PIL import Image
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    face_recognition = None
    Image = None

# Structured Logging Configuration
structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    wrapper_class=structlog.BoundLogger,
    logger_factory=structlog.PrintLoggerFactory(),
)
logger = structlog.get_logger()

if not FACE_RECOGNITION_AVAILABLE:
    logger.warning("`face_recognition` or `Pillow` library not found. Facial recognition features will be disabled.")

# Platform Enumeration
class PlatformType(str, Enum):
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"
    SNAPCHAT = "snapchat"
    TIKTOK = "tiktok"

# Security Configuration
class SecurityConfig(BaseModel):
    encryption_key: SecretStr = Field(..., min_length=32)
    key_rotation_days: int = Field(7, ge=1)
    sanitize_inputs: bool = True
    request_signatures: bool = True
    audit_logging: bool = True
    max_retries: int = Field(3, ge=1, le=10)
    max_file_size: int = Field(10_485_760, ge=1_048_576)  # 10MB

    @field_validator("encryption_key")
    @classmethod
    def validate_key_length(cls, v: SecretStr) -> SecretStr:
        try:
            key_bytes = v.get_secret_value().encode()
            decoded_key = base64.urlsafe_b64decode(key_bytes)
            if len(decoded_key) != 32:
                raise ValueError("Encryption key must decode to exactly 32 bytes.")
        except (TypeError, ValueError, base64.binascii.Error) as e:
            raise ValueError(f"Encryption key must be a valid URL-safe base64-encoded string: {e}")
        return v

    def rotate_key(self) -> None:
        new_raw_key = secrets.token_bytes(32)
        new_key = base64.urlsafe_b64encode(new_raw_key).decode()
        self.encryption_key = SecretStr(new_key)
        logger.info("Security key rotated successfully")

# Image Analysis Configuration
class ImageAnalysisConfig(BaseModel):
    enable_facial_recognition: bool = Field(True)
    face_detection_model: str = Field("hog")
    min_confidence_threshold: Optional[float] = Field(0.6, ge=0.0, le=1.0)

# Network Manager
class NetworkManager:
    def __init__(self, security: SecurityConfig):
        self.security = security
        self.client = httpx.AsyncClient(
            limits=httpx.Limits(max_connections=200),
            timeout=httpx.Timeout(30.0)
        )
        self.circuit_state = "closed"
        self.rate_limiter = asyncio.Semaphore(100)
        self.request_metrics = {"total": 0, "success": 0, "failures": 0, "latency": []}

    def _sign_request(self, headers: Dict) -> Dict:
        if self.security.request_signatures:
            timestamp = datetime.now().isoformat()
            payload = f"{timestamp}{self.security.encryption_key.get_secret_value()[:10]}"
            signature = hashlib.sha256(payload.encode()).hexdigest()
            headers.update({"X-Request-Signature": signature, "X-Timestamp": timestamp})
        return headers

    def _record_success(self, start_time: datetime) -> None:
        self.request_metrics["success"] += 1
        self.request_metrics["latency"].append((datetime.now() - start_time).total_seconds())
        if self.circuit_state == "half-open" or self.request_metrics["failures"] > 0:
            self.request_metrics["failures"] = max(0, self.request_metrics["failures"] - 1)
            if self.request_metrics["failures"] == 0 and self.circuit_state != "closed":
                self.circuit_state = "closed"
                logger.info("Circuit breaker closed")

    def _record_failure(self, start_time: datetime) -> None:
        self.request_metrics["failures"] += 1
        self.request_metrics["latency"].append((datetime.now() - start_time).total_seconds())
        failure_threshold = 10
        if self.circuit_state == "closed" and self.request_metrics["failures"] > failure_threshold:
            self.circuit_state = "open"
            logger.warning(f"Circuit breaker opened due to >{failure_threshold} failures")
            asyncio.create_task(self._try_reclose_circuit(delay=60))
        elif self.circuit_state == "half-open":
            self.circuit_state = "open"
            logger.warning("Circuit breaker re-opened after failure in half-open state")
            asyncio.create_task(self._try_reclose_circuit(delay=120))

    async def _try_reclose_circuit(self, delay: int):
        await asyncio.sleep(delay)
        if self.circuit_state == "open":
            self.circuit_state = "half-open"
            logger.info("Circuit breaker transitioned to half-open state")

    async def secure_request(self, method: str, url: str, **kwargs) -> Optional[httpx.Response]:
        if self.circuit_state == "open":
            logger.warning("Request blocked: Circuit breaker is open", url=url)
            error_response = httpx.Response(
                status_code=503,
                request=httpx.Request(method, url),
                text="Service Unavailable (Circuit Breaker Open)"
            )
            raise httpx.HTTPStatusError("Circuit breaker open", request=error_response.request, response=error_response)

        self.request_metrics["total"] += 1
        async with self.rate_limiter:
            start_time = datetime.now()
            response = None
            try:
                async for attempt in AsyncRetrying(
                    stop=stop_after_attempt(self.security.max_retries),
                    wait=wait_exponential(multiplier=1, min=1, max=10),
                    retry=retry_if_exception_type((httpx.NetworkError, httpx.TimeoutException, httpx.ConnectError, httpx.HTTPStatusError)),
                    retry_error_callback=lambda retry_state: logger.warning(
                        f"Retrying request", url=url, attempt=retry_state.attempt_number, error=str(retry_state.outcome.exception())
                    ),
                    reraise=True
                ):
                    with attempt:
                        headers = self._sign_request(kwargs.pop("headers", {}))
                        log = logger.bind(method=method, url=url, attempt=attempt.retry_state.attempt_number)
                        log.debug("Making request")
                        response = await self.client.request(method, url, headers=headers, **kwargs)
                        log = log.bind(status_code=response.status_code)

                        if response.status_code >= 500 or response.status_code == 429:
                            log.warning("Server error or rate limit encountered")
                            response.raise_for_status()
                        response.raise_for_status()

                        if self.circuit_state == "half-open":
                            self.circuit_state = "closed"
                            logger.info("Circuit breaker closed after successful request in half-open state")

                        self._record_success(start_time)
                        log.info("Request successful")
                        return response

            except httpx.HTTPStatusError as http_err:
                self._record_failure(start_time)
                logger.error(
                    f"HTTP request failed after retries with status {http_err.response.status_code}",
                    url=url, method=method, response_text=http_err.response.text[:200]
                )
                raise
            except Exception as exc:
                self._record_failure(start_time)
                logger.error(
                    f"Network request failed after retries",
                    exc_info=True, url=url, method=method,
                    exception_type=type(exc).__name__, exception_message=str(exc)
                )
                return response

    async def close(self):
        await self.client.aclose()
        logger.info("NetworkManager client closed.")

# Platform Integrator Base Class
class PlatformIntegrator:
    def __init__(self, config: Dict, network: NetworkManager):
        platform_name = self.__class__.__name__.lower().replace('integrator', '')
        self.config = config
        self.network = network
        log_config = {k: v for k, v in config.items() if k not in ['rapidapi_key', 'client_secret', 'client_id']}
        logger.debug(f"Initializing {platform_name} integrator", config=log_config)

    async def search(self, name: str) -> Dict:
        raise NotImplementedError("Subclasses must implement search")

# Platform Integrators
class FacebookIntegrator(PlatformIntegrator):
    async def search(self, name: str) -> Dict:
        logger.warning("Facebook search requested, but no real API integration is implemented.", search_term=name)
        return {"matches": [], "error": "Facebook API integration not implemented."}

class TikTokIntegrator(PlatformIntegrator):
    async def search(self, name: str) -> Dict:
        logger.warning("TikTok search requested, but no real API integration is implemented.", search_term=name)
        return {"matches": [], "error": "TikTok API integration not implemented."}

class InstagramIntegrator(PlatformIntegrator):
    async def search(self, name: str) -> Dict:
        api_type = self.config.get('api_type')
        logger.info("Instagram search initiated", search_term=name, api_type=api_type)

        if api_type == 'rapidapi':
            user_id_url = self.config.get('api_endpoint')
            rapidapi_host = self.config.get('rapidapi_host')
            rapidapi_key_secret = self.config.get('rapidapi_key')

            if not user_id_url or not rapidapi_host or not rapidapi_key_secret:
                logger.error("RapidAPI configuration (endpoint, host, key) missing in Instagram config")
                return {"matches": [], "error": "Instagram API configuration incomplete."}

            rapidapi_key = rapidapi_key_secret.get_secret_value() if rapidapi_key_secret else None
            if not rapidapi_key:
                logger.error("RapidAPI key is missing.")
                return {"matches": [], "error": "Instagram API key missing."}

            headers = {
                'x-rapidapi-host': rapidapi_host,
                'x-rapidapi-key': rapidapi_key,
                'Content-Type': 'application/json'
            }
            payload = {'query': name}
            logger.info(f"Sending POST request to Instagram API", url=user_id_url, payload=payload)

            try:
                response = await self.network.secure_request("POST", user_id_url, headers=headers, json=payload)
                if response is None:
                    logger.error("API request returned None", url=user_id_url)
                    return {"matches": [], "error": "API request failed: No response received"}

                user_data = response.json()
                logger.debug("Instagram API response received", response_status=response.status_code, raw_response=user_data)

                matches = []
                try:
                    users = user_data.get('response', {}).get('body', {}).get('users', [])
                    if not users:
                        logger.warning(f"No users found for query: {name}")
                        return {"matches": [], "error": "No users found in API response"}

                    # Filter for accuracy: Prioritize verified accounts or high follower counts
                    for user in users:
                        user_info = user.get('user', {})
                        username = user_info.get('username')
                        user_id = user_info.get('pk') or user_info.get('id') or username
                        if not username:
                            continue

                        # Skip if name doesn't closely match
                        full_name = user_info.get('full_name', '').lower()
                        search_name = name.lower()
                        if search_name not in full_name and search_name not in username.lower():
                            continue

                        # Prioritize verified accounts or those with significant followers
                        is_verified = user_info.get('is_verified', False)
                        follower_count = user_info.get('follower_count', 0) or 0
                        if not is_verified and follower_count < 1000:
                            logger.debug(f"Skipping low-relevance account: {username} (not verified, {follower_count} followers)")
                            continue

                        match = {
                            "id": str(user_id),
                            "name": user_info.get('full_name', name),
                            "username": username,
                            "platform": "instagram",
                            "profile_url": f"https://www.instagram.com/{username}",
                            "is_verified": is_verified,
                            "followers": follower_count,
                            "following": user_info.get('following_count', None),
                            "posts": user_info.get('media_count', None),
                            "bio": user_info.get('biography', ''),
                            "profile_pic_url": user_info.get('profile_pic_url', None)
                        }
                        match = {k: v for k, v in match.items() if v is not None}
                        matches.append(match)

                    if len(matches) > 5:
                        logger.warning(f"Multiple matches ({len(matches)}) found for '{name}'. Consider refining the search term.")
                    logger.info(f"Found {len(matches)} Instagram user(s) for query: {name}")
                    return {"matches": matches}

                except (KeyError, TypeError) as e:
                    logger.error(f"Failed to parse Instagram API response: {str(e)}", response_keys=list(user_data.keys()) if isinstance(user_data, dict) else type(user_data))
                    return {"matches": [], "error": f"Invalid response format: {str(e)}"}

            except httpx.HTTPStatusError as http_err:
                error_detail = f"Instagram API HTTP error: {http_err.response.status_code}"
                try:
                    error_detail += f" - {http_err.response.text[:200]}"
                except Exception:
                    pass
                logger.error(error_detail)
                return {"matches": [], "error": error_detail}
            except Exception as api_error:
                logger.error(f"Instagram RapidAPI request failed", exc_info=True)
                return {"matches": [], "error": f"Instagram API request failed: {str(api_error)}"}
        else:
            logger.warning(f"Instagram search skipped: Configured api_type ('{api_type}') is not 'rapidapi'.")
            return {"matches": [], "error": "Instagram API not configured or unsupported type."}

# Async Database (Simulated)
class AsyncDatabase:
    def __init__(self, dsn: str):
        self.dsn = dsn
        self.connected = False

    async def connect(self):
        try:
            await asyncio.sleep(0.05)
            self.connected = True
            logger.info("Database connection established (simulated)", dsn=self.dsn)
        except Exception as e:
            logger.error("Database connection failed (simulated)", dsn=self.dsn, error=str(e))
            self.connected = False

    async def disconnect(self):
        if self.connected:
            await asyncio.sleep(0.01)
            self.connected = False
            logger.info("Database connection closed (simulated)")

    async def log_audit(self, event: str, details: Dict):
        if self.connected:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event": event,
                "details": details
            }
            try:
                await asyncio.sleep(0.005)
                logger.debug("Audit event logged to DB (simulated)", audit_event_data=log_entry)
            except Exception as e:
                logger.error("Failed to write audit log to DB (simulated)", error=str(e), audit_event_name=event)
        else:
            logger.warning("Audit log skipped: Database not connected", audit_event_name=event)

# Image Analysis Engine
class ImageAnalysisEngine:
    def __init__(self, security: SecurityConfig, analysis_config: ImageAnalysisConfig):
        self.security = security
        self.analysis_config = analysis_config
        self.network = NetworkManager(security)
        try:
            key_bytes = base64.urlsafe_b64decode(self.security.encryption_key.get_secret_value().encode())
            if len(key_bytes) != 32:
                raise ValueError("Decoded encryption key must be 32 bytes long.")
            self.fernet = Fernet(self.security.encryption_key.get_secret_value().encode())
        except (TypeError, ValueError, base64.binascii.Error) as e:
            logger.error("Failed to initialize Fernet cipher.", exc_info=True)
            raise ValueError(f"Invalid encryption key for Fernet initialization: {e}")

        self.mime = magic.Magic(mime=True)
        self._check_dependencies()

    def _check_dependencies(self):
        if self.analysis_config.enable_facial_recognition and not FACE_RECOGNITION_AVAILABLE:
            logger.error("Facial recognition enabled, but `face_recognition` or `Pillow` missing!")
            self.analysis_config.enable_facial_recognition = False
            logger.warning("Disabling facial recognition due to missing dependencies.")

    async def analyze(self, photo_path: Path, profile_pics: List[Dict] = None) -> Dict:
        analysis_result = {
            "status": "pending",
            "file_path": str(photo_path),
            "file_type": None,
            "size_bytes": None,
            "error": None,
            "facial_recognition_enabled": self.analysis_config.enable_facial_recognition,
            "face_count": 0,
            "face_locations": [],
            "face_encodings": [],
            "profile_pic_matches": []
        }

        try:
            if not await asyncio.to_thread(photo_path.exists):
                raise FileNotFoundError(f"Image file not found: '{photo_path}'")

            stat_result = await asyncio.to_thread(photo_path.stat)
            analysis_result["size_bytes"] = stat_result.st_size
            if analysis_result["size_bytes"] > self.security.max_file_size:
                raise ValueError(f"File size ({analysis_result['size_bytes']} bytes) exceeds limit.")

            file_type = await asyncio.to_thread(self.mime.from_file, str(photo_path))
            analysis_result["file_type"] = file_type
            if not file_type or not file_type.startswith("image/"):
                raise ValueError(f"Invalid file type: '{file_type}'. Expected an image.")
            logger.info("Image validated", path=str(photo_path), type=file_type, size=analysis_result["size_bytes"])

            async with aiofiles.open(photo_path, "rb") as f:
                image_content = await f.read()

            if self.analysis_config.enable_facial_recognition and FACE_RECOGNITION_AVAILABLE:
                logger.debug("Starting facial recognition", model=self.analysis_config.face_detection_model)
                try:
                    face_locations, face_encodings = await asyncio.to_thread(
                        self._perform_face_recognition,
                        image_content,
                        self.analysis_config.face_detection_model
                    )
                    analysis_result["face_count"] = len(face_locations)
                    analysis_result["face_locations"] = face_locations
                    analysis_result["face_encodings"] = [enc.tolist() for enc in face_encodings]
                    logger.info(f"Facial recognition completed. Found {len(face_locations)} face(s).")

                    # Compare with profile pictures if provided
                    if profile_pics and face_encodings:
                        analysis_result["profile_pic_matches"] = await self._compare_with_profile_pics(
                            face_encodings, profile_pics
                        )

                except Exception as fr_err:
                    logger.error("Facial recognition failed", exc_info=True)
                    analysis_result["error"] = f"Facial recognition error: {str(fr_err)}"

            analysis_result["status"] = "analyzed"

        except FileNotFoundError as e:
            logger.error("Image analysis error: File not found", path=str(photo_path))
            analysis_result["status"] = "error"
            analysis_result["error"] = str(e)
        except ValueError as e:
            logger.error(f"Image analysis error: Validation failed", path=str(photo_path), error=str(e))
            analysis_result["status"] = "error"
            analysis_result["error"] = str(e)
        except Exception as e:
            logger.error("Unexpected error during image analysis", path=str(photo_path), exc_info=True)
            analysis_result["status"] = "error"
            analysis_result["error"] = f"Unexpected error: {str(e)}"

        if analysis_result["status"] != "error" and analysis_result["error"] and analysis_result["error"].startswith("Facial recognition error"):
            analysis_result["status"] = "failed_analysis"

        return analysis_result

    async def _compare_with_profile_pics(self, input_encodings: List, profile_pics: List[Dict]) -> List[Dict]:
        matches = []
        for profile in profile_pics:
            username = profile.get("username")
            profile_pic_url = profile.get("profile_pic_url")
            if not profile_pic_url:
                logger.debug(f"No profile picture for {username}, skipping facial comparison")
                continue

            try:
                # Download profile picture
                response = await self.network.secure_request("GET", profile_pic_url)
                if response is None or response.status_code != 200:
                    logger.warning(f"Failed to download profile picture for {username}", url=profile_pic_url)
                    continue

                pic_content = response.content
                pic_type = self.mime.from_buffer(pic_content)
                if not pic_type.startswith("image/"):
                    logger.warning(f"Invalid profile picture type for {username}: {pic_type}")
                    continue

                # Perform facial recognition on profile picture
                profile_locations, profile_encodings = await asyncio.to_thread(
                    self._perform_face_recognition, pic_content, self.analysis_config.face_detection_model
                )

                if not profile_encodings:
                    logger.debug(f"No faces found in profile picture for {username}")
                    continue

                # Compare encodings
                for input_enc in input_encodings:
                    results = face_recognition.compare_faces(
                        profile_encodings, input_enc, tolerance=self.analysis_config.min_confidence_threshold
                    )
                    if any(results):
                        matches.append({
                            "username": username,
                            "profile_url": profile.get("profile_url"),
                            "match_confidence": float(face_recognition.face_distance(profile_encodings, input_enc).min()),
                            "profile_pic_url": profile_pic_url
                        })
                        logger.info(f"Face match found for {username}")

            except Exception as e:
                logger.error(f"Failed to process profile picture for {username}", exc_info=True)

        return matches

    def _perform_face_recognition(self, image_bytes: bytes, model: str) -> Tuple[List[Tuple[int, int, int, int]], List[Any]]:
        if not FACE_RECOGNITION_AVAILABLE:
            return [], []

        try:
            img_stream = io.BytesIO(image_bytes)
            img = Image.open(img_stream)
            img_rgb = img.convert("RGB")
            img_array = face_recognition.load_image_file(img_stream)
            face_locations = face_recognition.face_locations(img_array, model=model)
            if not face_locations:
                return [], []
            face_encodings = face_recognition.face_encodings(img_array, known_face_locations=face_locations)
            return face_locations, face_encodings
        except Exception as e:
            logger.error(f"Error in face recognition ({model} model)", exc_info=True)
            raise RuntimeError(f"Face recognition internal error: {e}")

# Core Application
class SocialMapperPro:
    def __init__(self, config: Dict[str, Any]):
        try:
            self.security_config = SecurityConfig(**config["security"])
            self.image_analysis_config = ImageAnalysisConfig(**config.get("image_analysis", {}))
            self.platform_configs = {}
            for p_type_str, p_config_dict in config["platforms"].items():
                try:
                    p_type = PlatformType(p_type_str)
                    if p_config_dict.get("enabled", False):
                        self.platform_configs[p_type] = PlatformConfig(**p_config_dict)
                    else:
                        logger.info(f"Platform 'PlatformType.{p_type_str.upper()}' is disabled in configuration.")
                except ValueError:
                    logger.warning(f"Invalid platform type '{p_type_str}' found in config, skipping.")
                except Exception as e:
                    logger.error(f"Invalid configuration for platform '{p_type_str}': {e}", exc_info=True)

            self.database_config = DatabaseConfig(**config["database"])
        except Exception as e:
            logger.critical(f"Core configuration validation failed: {e}", exc_info=True)
            raise ValueError(f"Configuration error: {e}")

        self.database = AsyncDatabase(self.database_config.dsn)
        self.network = NetworkManager(self.security_config)
        self.image_analyzer = ImageAnalysisEngine(self.security_config, self.image_analysis_config)
        self.integrators: Dict[str, PlatformIntegrator] = {}
        self._init_platforms()
        self._key_rotation_task = None
        logger.info("SocialMapperPro components initialized")

    def _init_platforms(self):
        integrator_classes = {
            PlatformType.FACEBOOK: FacebookIntegrator,
            PlatformType.TIKTOK: TikTokIntegrator,
            PlatformType.INSTAGRAM: InstagramIntegrator
        }
        for platform_type, platform_config in self.platform_configs.items():
            platform_str = platform_type.value
            if platform_type in integrator_classes:
                cls = integrator_classes[platform_type]
                self.integrators[platform_str] = cls(platform_config.model_dump(), self.network)
                logger.debug(f"Initialized integrator for enabled platform: {platform_str}")
            else:
                logger.warning(f"No integrator class found for configured platform: {platform_str}")

    def _schedule_key_rotation(self):
        if self._key_rotation_task is not None:
            logger.warning("Key rotation task already scheduled.")
            return

        async def rotate_periodically():
            while True:
                try:
                    rotation_interval_seconds = self.security_config.key_rotation_days * 86400
                    logger.info(f"Scheduling next key rotation in {rotation_interval_seconds} seconds.")
                    await asyncio.sleep(rotation_interval_seconds)
                    logger.info("Performing scheduled key rotation...")
                    self.security_config.rotate_key()
                    try:
                        self.image_analyzer.fernet = Fernet(self.security_config.encryption_key.get_secret_value().encode())
                        logger.info("Image analysis engine crypto updated with new key.")
                    except Exception as fernet_err:
                        logger.critical("CRITICAL: Failed to update Fernet key after rotation.", exc_info=True)
                    if self.security_config.audit_logging:
                        await self.log_audit_event("SecurityKeyRotated", {"new_key_status": "success"})
                except asyncio.CancelledError:
                    logger.info("Key rotation task cancelled.")
                    break
                except Exception as rotation_err:
                    logger.error("Failed during periodic key rotation", exc_info=True)
                    if self.security_config.audit_logging:
                        await self.log_audit_event("SecurityKeyRotationFailed", {"error": str(rotation_err)})
                    await asyncio.sleep(3600)

        if self.security_config.key_rotation_days > 0:
            self._key_rotation_task = asyncio.create_task(rotate_periodically())
            logger.info("Scheduled periodic security key rotation.")
        else:
            logger.warning("Key rotation disabled (key_rotation_days <= 0).")

    async def initialize(self):
        await self.database.connect()
        self._schedule_key_rotation()
        if self.security_config.audit_logging:
            await self.log_audit_event("SystemInitialized", {"status": "success"})
        logger.info("SocialMapperPro initialized successfully.")

    async def shutdown(self):
        logger.info("Shutting down SocialMapperPro...")
        if self._key_rotation_task and not self._key_rotation_task.done():
            self._key_rotation_task.cancel()
            try:
                await self._key_rotation_task
            except asyncio.CancelledError:
                logger.info("Key rotation task successfully cancelled.")
            except Exception as e:
                logger.error("Error during key rotation task cancellation", exc_info=True)
        if self.security_config.audit_logging:
            try:
                await self.log_audit_event("SystemShutdown", {"status": "success"})
            except Exception as e:
                logger.error("Failed to log SystemShutdown audit event", exc_info=True)
        await self.network.close()
        await self.database.disconnect()
        logger.info("SocialMapperPro shut down completed.")

    async def log_audit_event(self, event_name: str, details: Dict):
        if self.security_config.audit_logging:
            sanitized_details = self._sanitize_audit_details(details)
            await self.database.log_audit(event_name, sanitized_details)
        else:
            logger.debug("Audit logging disabled, skipping event", audit_event_name=event_name)

    def _sanitize_audit_details(self, details: Dict) -> Dict:
        sanitized = {}
        sensitive_keys = ['key', 'password', 'secret', 'token', 'encryption_key', 'rapidapi_key', 'client_secret']
        for k, v in details.items():
            if isinstance(v, dict):
                sanitized[k] = self._sanitize_audit_details(v)
            elif isinstance(v, SecretStr):
                sanitized[k] = "***REDACTED***"
            elif any(sens_key in k.lower() for sens_key in sensitive_keys):
                sanitized[k] = "***REDACTED***"
            else:
                if isinstance(v, Path):
                    sanitized[k] = str(v)
                else:
                    sanitized[k] = v
        return sanitized

    async def _search_platform(self, platform: str, name: str) -> Dict:
        log = logger.bind(platform=platform, search_term=name)
        if platform not in self.integrators:
            log.warning("Integrator not available/enabled for platform")
            return {"status": "error", "error": f"Platform '{platform}' not configured or supported."}

        log.info("Initiating platform search")
        integrator = self.integrators[platform]
        try:
            await self.log_audit_event("PlatformSearchStarted", {"platform": platform, "name": name})
            
            if platform == "instagram":
                instagram_config = self.platform_configs[PlatformType.INSTAGRAM]
                headers = {
                    "Content-Type": "application/json",
                    "x-rapidapi-host": instagram_config.rapidapi_host,
                    "x-rapidapi-key": instagram_config.rapidapi_key.get_secret_value()
                }
                data = {"query": name}
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        instagram_config.api_endpoint,
                        headers=headers,
                        json=data,
                        timeout=10.0
                    )
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        users = response_data.get('response', {}).get('body', {}).get('users', [])
                        matches = []
                        for user in users:
                            user_info = user.get('user', {})
                            matches.append({
                                "username": user_info.get('username'),
                                "full_name": user_info.get('full_name'),
                                "is_verified": user_info.get('is_verified', False),
                                "profile_pic_url": user_info.get('profile_pic_url'),
                                "profile_url": f"https://www.instagram.com/{user_info.get('username')}"
                            })
                        result = {"matches": matches, "error": None}
                    else:
                        result = {"matches": [], "error": f"Instagram API error: {response.status_code} - {response.text}"}
            else:
                result = await integrator.search(name)

            final_result = {
                "status": "completed" if "matches" in result and "error" not in result else "error",
                "matches": result.get("matches", []),
                "error": result.get("error")
            }
            await self.log_audit_event("PlatformSearchCompleted", {
                "platform": platform,
                "name": name,
                "status": final_result["status"],
                "match_count": len(final_result["matches"]),
                "error": final_result["error"]
            })
            log.info("Platform search finished", status=final_result["status"], match_count=len(final_result["matches"]), error=final_result["error"])
            return final_result
        except Exception as exc:
            log.error("Platform search failed unexpectedly", exc_info=True)
            await self.log_audit_event("PlatformSearchFailed", {"platform": platform, "name": name, "error": str(exc)})
            return {"status": "error", "matches": [], "error": f"An unexpected error occurred during {platform} search: {str(exc)}"}

    async def _analyze_image(self, photo_path: Optional[Path], platform_results: Dict) -> Dict:
        log = logger.bind(photo_path=str(photo_path) if photo_path else None)
        if photo_path is None:
            log.info("Image analysis skipped: No photo provided.")
            return {"status": "skipped", "reason": "No photo provided"}

        log.info("Initiating image analysis")
        try:
            await self.log_audit_event("ImageAnalysisStarted", {"photo_path": str(photo_path)})
            # Extract profile pictures from platform results
            profile_pics = []
            for platform, result in platform_results.items():
                if result.get("status") == "completed":
                    for match in result.get("matches", []):
                        if match.get("profile_pic_url"):
                            profile_pics.append({
                                "username": match["username"],
                                "profile_url": match["profile_url"],
                                "profile_pic_url": match["profile_pic_url"]
                            })

            result = await self.image_analyzer.analyze(photo_path, profile_pics)
            await self.log_audit_event("ImageAnalysisCompleted", {
                "photo_path": str(photo_path),
                "status": result.get("status"),
                "face_count": result.get("face_count"),
                "match_count": len(result.get("profile_pic_matches", [])),
                "error": result.get("error")
            })
            log.info("Image analysis completed", status=result.get('status'), faces_found=result.get('face_count', 0), matches_found=len(result.get("profile_pic_matches", [])), error=result.get('error'))
            return result
        except Exception as exc:
            log.error("Image analysis task failed unexpectedly", exc_info=True)
            await self.log_audit_event("ImageAnalysisFailed", {"photo_path": str(photo_path), "error": str(exc)})
            return {"status": "error", "error": f"An unexpected error occurred calling image analysis: {str(exc)}"}

    def _generate_summary(self, results: Dict) -> Dict:
        total_matches = 0
        platform_statuses = {}
        errors = []
        for platform, result in results["platforms"].items():
            status = result.get("status", "unknown")
            platform_statuses[platform] = status
            if status == "completed":
                total_matches += len(result.get("matches", []))
            elif status == "error":
                errors.append(f"{platform}: {result.get('error', 'Unknown error')}")

        image_status = results.get("image_analysis", {}).get("status", "not_run")
        if image_status == "error" or image_status == "failed_analysis":
            errors.append(f"Image Analysis: {results['image_analysis'].get('error', 'Unknown error')}")

        return {
            "overall_status": "completed" if not errors else "completed_with_errors",
            "total_matches_found": total_matches,
            "platforms_queried": list(results["platforms"].keys()),
            "platform_statuses": platform_statuses,
            "image_analysis_status": image_status,
            "face_match_count": len(results.get("image_analysis", {}).get("profile_pic_matches", [])),
            "errors_encountered": errors if errors else None
        }

    async def full_analysis(self, name: str, photo_path: Optional[Path], platforms_to_search: List[str]) -> Dict:
        start_time = datetime.now()
        query_id = secrets.token_hex(8)
        log = logger.bind(query_id=query_id, name=name, photo_path=str(photo_path) if photo_path else "None", platforms=platforms_to_search)
        log.info("Starting full analysis")

        if not name or not name.strip():
            log.error("Analysis cannot proceed without a name.")
            return {"query_id": query_id, "error": "Name cannot be empty."}

        await self.log_audit_event("FullAnalysisStarted", {
            "query_id": query_id, "name": name, "photo_provided": photo_path is not None, "platforms": platforms_to_search
        })

        results = {
            "query_id": query_id,
            "query_details": {
                "name": name,
                "photo_path": str(photo_path) if photo_path else None,
                "platforms_requested": platforms_to_search,
                "timestamp": start_time.isoformat(),
            },
            "platform_results": {},
            "image_analysis_result": {},
            "summary": {}
        }

        search_tasks = []
        valid_platforms_queued = []
        for platform_str_raw in platforms_to_search:
            platform_str = platform_str_raw.lower()
            if platform_str in self.integrators:
                task = asyncio.create_task(self._search_platform(platform_str, name), name=f"search-{platform_str}")
                search_tasks.append((platform_str, task))
                valid_platforms_queued.append(platform_str)
                results["platform_results"][platform_str] = {"status": "pending"}
            else:
                log.warning(f"Skipping unsupported/disabled platform: {platform_str}")
                results["platform_results"][platform_str] = {"status": "skipped", "reason": "Not configured or disabled"}

        platform_task_results = await asyncio.gather(*[task for _, task in search_tasks], return_exceptions=True)
        for i, (platform, _) in enumerate(search_tasks):
            result_or_exc = platform_task_results[i]
            if isinstance(result_or_exc, Exception):
                log.error(f"Platform search task failed unexpectedly for {platform}", exc_info=result_or_exc)
                results["platform_results"][platform] = {"status": "error", "matches": [], "error": f"Task execution failed: {str(result_or_exc)}"}
            else:
                results["platform_results"][platform] = result_or_exc

        # Perform image analysis after platform search to use profile pictures
        image_analysis_task = asyncio.create_task(
            self._analyze_image(photo_path, results["platform_results"]), name="image-analysis"
        )
        try:
            results["image_analysis_result"] = await image_analysis_task
        except Exception as img_exc:
            log.error("Image analysis task failed unexpectedly", exc_info=img_exc)
            results["image_analysis_result"] = {"status": "error", "error": f"Task execution failed: {str(img_exc)}"}

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        summary_input = {"platforms": results["platform_results"], "image_analysis": results["image_analysis_result"]}
        results["summary"] = self._generate_summary(summary_input)
        results["analysis_duration_seconds"] = round(duration, 3)

        log.info("Full analysis completed", duration=results["analysis_duration_seconds"], summary=results["summary"])
        await self.log_audit_event("FullAnalysisCompleted", {
            "query_id": query_id,
            "duration_seconds": results["analysis_duration_seconds"],
            "summary": results["summary"]
        })

        return results

# Configuration Models
class PlatformConfig(BaseModel):
    enabled: bool = False
    api_endpoint: Optional[str] = None
    api_type: Optional[str] = None
    rapidapi_host: Optional[str] = None
    rapidapi_key: Optional[SecretStr] = None
    default_region: Optional[str] = None
    default_count: Optional[int] = Field(None, ge=1)
    default_secUid: Optional[str] = None

    @field_validator('api_endpoint', 'rapidapi_host', 'rapidapi_key', mode='before')
    @classmethod
    def check_required_for_enabled(cls, v, info):
        if info.data.get('enabled') and info.data.get('api_type') == 'rapidapi':
            if info.field_name in ['rapidapi_host', 'rapidapi_key', 'api_endpoint']:
                if not v:
                    raise ValueError(f"'{info.field_name}' is required for enabled RapidAPI integration")
        if info.field_name == 'rapidapi_key' and v and not isinstance(v, SecretStr):
            return SecretStr(str(v))
        return v

class DatabaseConfig(BaseModel):
    dsn: str

class SearchConfig(BaseModel):
    default_name: str = "Example Name"
    photo_path: Optional[str] = None
    platforms_to_search: List[str] = ["tiktok", "instagram"]

class SocialMapperConfig(BaseModel):
    security: SecurityConfig
    database: DatabaseConfig
    platforms: Dict[PlatformType, PlatformConfig]
    image_analysis: ImageAnalysisConfig = Field(default_factory=ImageAnalysisConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)

    @field_validator('platforms')
    @classmethod
    def check_at_least_one_platform_enabled(cls, v):
        if not any(p_config.enabled for p_config in v.values()):
            logger.warning("Configuration validation: No platforms are enabled.")
        return v

# Main Function
async def main():
    config_path = Path("config.json")

    if not config_path.exists():
        logger.warning(f"{config_path} not found. Generating a default configuration file.")
        try:
            default_key = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode()
            default_config = {
                "security": {
                    "encryption_key": default_key,
                    "key_rotation_days": 7,
                    "sanitize_inputs": True,
                    "request_signatures": True,
                    "audit_logging": True,
                    "max_retries": 3,
                    "max_file_size": 10485760
                },
                "database": {
                    "dsn": "postgresql+asyncpg://user:pass@localhost:5432/socialsearchdb"
                },
                "platforms": {
                    "facebook": {"enabled": False},
                    "tiktok": {"enabled": False},
                    "instagram": {
                        "enabled": True,
                        "api_type": "rapidapi",
                        "api_endpoint": "https://rocketapi-for-developers.p.rapidapi.com/instagram/search",
                        "rapidapi_host": "rocketapi-for-developers.p.rapidapi.com",
                        "rapidapi_key": "YOUR_RAPIDAPI_KEY_HERE",
                        "default_count": 20
                    },
                    "linkedin": {"enabled": False},
                    "snapchat": {"enabled": False}
                },
                "image_analysis": {
                    "enable_facial_recognition": True,
                    "face_detection_model": "hog",
                    "min_confidence_threshold": 0.6
                },
                "search": {
                    "default_name": "Virat Kohli",
                    "photo_path": None,
                    "platforms_to_search": ["instagram"]
                }
            }
            with open(config_path, "w") as f:
                json.dump(default_config, f, indent=4)
            logger.info(f"Default configuration saved to {config_path}. Please update API keys and database DSN.")
            return
        except Exception as e:
            logger.error(f"Error generating default config: {e}", exc_info=True)
            return

    try:
        with open(config_path, "r") as f:
            config_data = json.load(f)
        validated_config = SocialMapperConfig(**config_data)
        logger.info("Configuration loaded and validated successfully.")
    except FileNotFoundError:
        logger.error(f"Configuration file {config_path} not found.")
        return
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in {config_path}. Please check the file format.")
        return
    except Exception as e:
        logger.error(f"Configuration loading or validation failed: {e}", exc_info=True)
        return

    # Validate RapidAPI key
    instagram_config = validated_config.platforms.get(PlatformType.INSTAGRAM)
    if instagram_config.enabled and instagram_config.rapidapi_key.get_secret_value() == "YOUR_RAPIDAPI_KEY_HERE":
        logger.error("Invalid RapidAPI key. Please update 'rapidapi_key' in config.json.")
        return

    mapper = None
    try:
        mapper = SocialMapperPro(validated_config.model_dump())
        await mapper.initialize()

        search_cfg = validated_config.search
        name_to_search = search_cfg.default_name.strip()
        photo_path_str = search_cfg.photo_path
        platforms_to_query = search_cfg.platforms_to_search

        enabled_platforms = [p.value for p, cfg in validated_config.platforms.items() if cfg.enabled]
        final_platforms_to_search = [p for p in platforms_to_query if p in enabled_platforms]
        if not final_platforms_to_search:
            logger.error(f"No platforms specified in 'search.platforms_to_search' are enabled. Requested: {platforms_to_query}, Enabled: {enabled_platforms}")
            return

        photo_path_resolved = None
        if photo_path_str:
            photo_path_resolved = Path(photo_path_str)
            if not await asyncio.to_thread(photo_path_resolved.is_file):
                logger.warning(f"Photo path specified ('{photo_path_str}') does not exist or is not a file. Image analysis will be skipped.")
                photo_path_resolved = None
            else:
                logger.info(f"Using photo for analysis: {photo_path_resolved.resolve()}")

        logger.info(f"Starting analysis for name='{name_to_search}'",
                    photo=str(photo_path_resolved) if photo_path_resolved else "N/A",
                    platforms=final_platforms_to_search)

        if not name_to_search:
            logger.error("Search name is empty. Aborting analysis.")
            return

        results = await mapper.full_analysis(
            name=name_to_search,
            photo_path=photo_path_resolved,
            platforms_to_search=final_platforms_to_search
        )

        # Save results to file with better error handling and absolute path
        try:
            # Get the current directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            output_path = os.path.join(current_dir, f"results_{results['query_id']}.json")
            print(f"Attempting to save results to {output_path}")
            with open(output_path, "w") as f:
                json.dump(results, f, indent=2)
            print(f"Results successfully saved to {output_path}")
            logger.info(f"Results saved to {output_path}")
        except Exception as save_error:
            print(f"Error saving results to file: {save_error}")
            logger.error(f"Failed to save results to file", exc_info=True)

        print("\n" + "="*30 + " Analysis Results " + "="*30)
        def format_results_for_print(data):
            if isinstance(data, dict):
                new_dict = {}
                for k, v in data.items():
                    if k == "face_encodings" and isinstance(v, list) and v:
                        new_dict[k] = f"[{len(v)} face encoding(s) found, omitted for brevity]"
                    else:
                        new_dict[k] = format_results_for_print(v)
                return new_dict
            elif isinstance(data, list):
                return [format_results_for_print(item) for item in data]
            elif isinstance(data, SecretStr):
                return "***REDACTED***"
            else:
                return data

        formatted_results = format_results_for_print(results)
        print(json.dumps(formatted_results, indent=2))
        print("="*80 + "\n")

    except ValueError as config_err:
        logger.critical(f"Initialization failed due to configuration error: {config_err}", exc_info=True)
    except Exception as e:
        logger.critical("A critical error occurred during execution.", exc_info=True)
    finally:
        if mapper:
            await mapper.shutdown()
        logger.info("Application finished.")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main())